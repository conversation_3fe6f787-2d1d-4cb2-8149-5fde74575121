{% extends 'base.html' %}
{% load static %}

{% block title %}登录 - 物资管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4><i class="fas fa-sign-in-alt"></i> 用户登录</h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">用户名</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger">{{ form.username.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">密码</label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger">{{ form.password.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors.0 }}
                        </div>
                    {% endif %}
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> 登录
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-3">
                    <a href="{% url 'accounts:register' %}" class="text-decoration-none">
                        还没有账号？立即注册
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
