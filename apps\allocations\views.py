from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q
from django.utils import timezone
from .models import AllocationRequest, AllocationRequestItem, Allocation
from .forms import AllocationRequestForm, AllocationRequestItemFormSet, AllocationApprovalForm
import uuid


class AllocationRequestListView(LoginRequiredMixin, ListView):
    """分配申请列表视图"""
    model = AllocationRequest
    template_name = 'allocations/request_list.html'
    context_object_name = 'requests'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = AllocationRequest.objects.select_related('department', 'requester', 'approver')
        
        # 根据用户角色过滤
        if not (self.request.user.is_manager or self.request.user.is_admin):
            queryset = queryset.filter(requester=self.request.user)
        
        # 状态筛选
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(request_number__icontains=search) |
                Q(title__icontains=search) |
                Q(description__icontains=search)
            )
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = AllocationRequest.STATUS_CHOICES
        context['selected_status'] = self.request.GET.get('status', '')
        context['search'] = self.request.GET.get('search', '')
        return context


@login_required
def request_create(request):
    """创建分配申请"""
    if request.method == 'POST':
        form = AllocationRequestForm(request.POST)
        formset = AllocationRequestItemFormSet(request.POST)
        
        if form.is_valid() and formset.is_valid():
            allocation_request = form.save(commit=False)
            allocation_request.requester = request.user
            allocation_request.request_number = f"REQ{uuid.uuid4().hex[:8].upper()}"
            allocation_request.save()
            
            formset.instance = allocation_request
            formset.save()
            
            messages.success(request, '分配申请创建成功。')
            return redirect('allocations:request_list')
    else:
        form = AllocationRequestForm()
        formset = AllocationRequestItemFormSet()
    
    context = {
        'form': form,
        'formset': formset,
    }
    return render(request, 'allocations/request_form.html', context)


@login_required
def request_detail(request, pk):
    """申请详情"""
    allocation_request = get_object_or_404(AllocationRequest, pk=pk)
    
    # 权限检查
    if not (allocation_request.requester == request.user or 
            request.user.is_manager or request.user.is_admin):
        messages.error(request, '您没有权限查看此申请。')
        return redirect('allocations:request_list')
    
    context = {
        'request': allocation_request,
        'items': allocation_request.items.select_related('material'),
    }
    return render(request, 'allocations/request_detail.html', context)


@login_required
def request_approve(request, pk):
    """审批申请"""
    allocation_request = get_object_or_404(AllocationRequest, pk=pk)
    
    # 权限检查
    if not allocation_request.can_approve(request.user):
        messages.error(request, '您没有权限审批此申请。')
        return redirect('allocations:request_detail', pk=pk)
    
    if allocation_request.status != 'pending':
        messages.error(request, '此申请已经处理过了。')
        return redirect('allocations:request_detail', pk=pk)
    
    if request.method == 'POST':
        form = AllocationApprovalForm(request.POST, instance=allocation_request)
        if form.is_valid():
            allocation_request = form.save(commit=False)
            allocation_request.approver = request.user
            allocation_request.approved_at = timezone.now()
            allocation_request.save()
            
            # 更新明细的批准数量
            for item_data in form.cleaned_data.get('items', []):
                item = AllocationRequestItem.objects.get(id=item_data['id'])
                item.approved_quantity = item_data['approved_quantity']
                item.save()
            
            messages.success(request, '申请审批完成。')
            return redirect('allocations:request_detail', pk=pk)
    else:
        form = AllocationApprovalForm(instance=allocation_request)
    
    context = {
        'form': form,
        'request': allocation_request,
        'items': allocation_request.items.select_related('material'),
    }
    return render(request, 'allocations/request_approve.html', context)


class AllocationListView(LoginRequiredMixin, ListView):
    """分配记录列表视图"""
    model = Allocation
    template_name = 'allocations/allocation_list.html'
    context_object_name = 'allocations'
    paginate_by = 20
    
    def get_queryset(self):
        return Allocation.objects.select_related('department', 'allocator', 'request').order_by('-allocated_at')


@login_required
def allocation_detail(request, pk):
    """分配详情"""
    allocation = get_object_or_404(Allocation, pk=pk)
    
    context = {
        'allocation': allocation,
        'items': allocation.items.select_related('material'),
    }
    return render(request, 'allocations/allocation_detail.html', context)
