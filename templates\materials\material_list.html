{% extends 'base.html' %}
{% load static %}

{% block title %}物资列表 - 物资管理系统{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'accounts:dashboard' %}">首页</a></li>
        <li class="breadcrumb-item active">物资列表</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2><i class="fas fa-box"></i> 物资列表</h2>
            <a href="{% url 'materials:create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加物资
            </a>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" 
                               placeholder="搜索物资名称、编码或规格" value="{{ search }}">
                    </div>
                    <div class="col-md-3">
                        <select name="category" class="form-control">
                            <option value="">所有分类</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if category.id|stringformat:"s" == selected_category %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'materials:list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-undo"></i> 重置
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 物资列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if materials %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>物资编码</th>
                                <th>物资名称</th>
                                <th>分类</th>
                                <th>规格型号</th>
                                <th>单位</th>
                                <th>单价</th>
                                <th>当前库存</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material in materials %}
                            <tr>
                                <td>{{ material.code }}</td>
                                <td>
                                    <a href="{% url 'materials:detail' material.pk %}" class="text-decoration-none">
                                        {{ material.name }}
                                    </a>
                                </td>
                                <td>{{ material.category.name }}</td>
                                <td>{{ material.specification|default:"-" }}</td>
                                <td>{{ material.get_unit_display }}</td>
                                <td>¥{{ material.unit_price }}</td>
                                <td>
                                    <span class="{% if material.is_low_stock %}text-danger{% endif %}">
                                        {{ material.current_stock }}
                                        {% if material.is_low_stock %}
                                            <i class="fas fa-exclamation-triangle text-warning" title="库存不足"></i>
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if material.is_active %}
                                        <span class="badge bg-success">启用</span>
                                    {% else %}
                                        <span class="badge bg-secondary">禁用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'materials:detail' material.pk %}" 
                                           class="btn btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'materials:edit' material.pk %}" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if is_paginated %}
                <nav aria-label="分页导航">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无物资数据</h5>
                    <p class="text-muted">点击上方按钮添加第一个物资</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
