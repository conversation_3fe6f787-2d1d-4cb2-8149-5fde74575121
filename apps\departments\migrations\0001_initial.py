# Generated by Django 3.2.25 on 2025-06-30 03:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='科室名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='科室代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='科室描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('head', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='headed_departments', to=settings.AUTH_USER_MODEL, verbose_name='科室主任')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='departments.department', verbose_name='上级科室')),
            ],
            options={
                'verbose_name': '科室',
                'verbose_name_plural': '科室',
                'db_table': 'departments_department',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='DepartmentBudget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(verbose_name='年度')),
                ('total_budget', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='总预算')),
                ('used_budget', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='已使用预算')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='budgets', to='departments.department', verbose_name='科室')),
            ],
            options={
                'verbose_name': '科室预算',
                'verbose_name_plural': '科室预算',
                'db_table': 'departments_budget',
                'ordering': ['-year'],
                'unique_together': {('department', 'year')},
            },
        ),
    ]
