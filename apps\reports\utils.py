import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from django.http import HttpResponse
from django.utils import timezone
import io


def generate_excel_report(data, report_type):
    """生成Excel报表"""
    wb = openpyxl.Workbook()
    ws = wb.active
    
    # 设置标题样式
    title_font = Font(bold=True, size=14)
    header_font = Font(bold=True, size=12)
    header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
    
    if report_type == 'material_stock_report':
        ws.title = "物资库存报表"
        
        # 标题
        ws['A1'] = "物资库存报表"
        ws['A1'].font = title_font
        ws.merge_cells('A1:H1')
        
        # 生成时间
        ws['A2'] = f"生成时间: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        # 表头
        headers = ['物资编码', '物资名称', '分类', '规格型号', '单位', '单价', '当前库存', '最低库存']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=4, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center')
        
        # 数据行
        for row, material in enumerate(data, 5):
            ws.cell(row=row, column=1, value=material.code)
            ws.cell(row=row, column=2, value=material.name)
            ws.cell(row=row, column=3, value=material.category.name)
            ws.cell(row=row, column=4, value=material.specification or '')
            ws.cell(row=row, column=5, value=material.get_unit_display())
            ws.cell(row=row, column=6, value=float(material.unit_price))
            ws.cell(row=row, column=7, value=material.current_stock)
            ws.cell(row=row, column=8, value=material.min_stock)
        
        # 调整列宽
        column_widths = [15, 25, 15, 20, 8, 12, 12, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
    
    # 保存到内存
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    # 创建HTTP响应
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{report_type}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
    
    return response


def generate_pdf_report(data, report_type):
    """生成PDF报表"""
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    
    # 创建PDF文档
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    
    # 样式
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # 居中
    )
    
    # 内容列表
    story = []
    
    if report_type == 'material_stock_report':
        # 标题
        title = Paragraph("物资库存报表", title_style)
        story.append(title)
        
        # 生成时间
        time_text = f"生成时间: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
        story.append(Paragraph(time_text, styles['Normal']))
        story.append(Spacer(1, 20))
        
        # 表格数据
        table_data = [
            ['物资编码', '物资名称', '分类', '单价', '当前库存', '最低库存']
        ]
        
        for material in data:
            table_data.append([
                material.code,
                material.name,
                material.category.name,
                f"¥{material.unit_price}",
                str(material.current_stock),
                str(material.min_stock)
            ])
        
        # 创建表格
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
    
    # 构建PDF
    doc.build(story)
    
    # 创建HTTP响应
    buffer.seek(0)
    response = HttpResponse(
        buffer.getvalue(),
        content_type='application/pdf'
    )
    response['Content-Disposition'] = f'attachment; filename="{report_type}_{timezone.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
    
    return response
