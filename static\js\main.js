// 主要JavaScript功能

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 自动隐藏消息提示
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // 确认删除对话框
    $('.delete-confirm').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var title = $(this).data('title') || '确认删除';
        var message = $(this).data('message') || '此操作不可撤销，确定要删除吗？';
        
        if (confirm(title + '\n\n' + message)) {
            window.location.href = url;
        }
    });

    // 表格行点击事件
    $('.table-row-clickable').on('click', function() {
        var url = $(this).data('url');
        if (url) {
            window.location.href = url;
        }
    });

    // 搜索框自动提交
    $('.search-auto-submit').on('input', debounce(function() {
        $(this).closest('form').submit();
    }, 500));

    // 数字输入框验证
    $('.number-input').on('input', function() {
        var value = $(this).val();
        if (value && isNaN(value)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // 物资选择时自动填充单价
    $('select[name*="material"]').on('change', function() {
        var materialId = $(this).val();
        var priceInput = $(this).closest('.form-row, .row').find('input[name*="unit_price"]');
        
        if (materialId && priceInput.length) {
            $.ajax({
                url: '/api/materials/' + materialId + '/price/',
                method: 'GET',
                success: function(data) {
                    priceInput.val(data.unit_price);
                },
                error: function() {
                    console.log('获取物资价格失败');
                }
            });
        }
    });

    // 动态表单集合管理
    $('.add-form-row').on('click', function(e) {
        e.preventDefault();
        var formset = $(this).data('formset');
        var container = $('#' + formset + '-container');
        var totalForms = $('#id_' + formset + '-TOTAL_FORMS');
        var newIndex = parseInt(totalForms.val());
        
        // 克隆模板行
        var template = container.find('.form-row-template').first();
        var newRow = template.clone();
        
        // 更新表单字段名称和ID
        newRow.html(newRow.html().replace(/__prefix__/g, newIndex));
        newRow.removeClass('form-row-template d-none');
        newRow.addClass('form-row');
        
        // 添加到容器
        container.append(newRow);
        
        // 更新总表单数
        totalForms.val(newIndex + 1);
        
        // 重新初始化新行的事件
        initializeFormRow(newRow);
    });

    // 删除表单行
    $(document).on('click', '.remove-form-row', function(e) {
        e.preventDefault();
        var row = $(this).closest('.form-row');
        var deleteInput = row.find('input[name*="DELETE"]');
        
        if (deleteInput.length) {
            deleteInput.prop('checked', true);
            row.hide();
        } else {
            row.remove();
        }
    });

    // 批量操作
    $('#select-all').on('change', function() {
        $('.item-checkbox').prop('checked', $(this).prop('checked'));
        updateBatchActions();
    });

    $('.item-checkbox').on('change', function() {
        updateBatchActions();
    });

    $('.batch-action').on('click', function(e) {
        e.preventDefault();
        var action = $(this).data('action');
        var selectedItems = $('.item-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedItems.length === 0) {
            alert('请选择要操作的项目');
            return;
        }

        if (confirm('确定要对选中的 ' + selectedItems.length + ' 个项目执行此操作吗？')) {
            performBatchAction(action, selectedItems);
        }
    });

    // 图表初始化
    initializeCharts();
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 初始化表单行
function initializeFormRow(row) {
    // 重新初始化选择框
    row.find('select').each(function() {
        $(this).trigger('change');
    });
    
    // 重新绑定事件
    row.find('.number-input').on('input', function() {
        var value = $(this).val();
        if (value && isNaN(value)) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
}

// 更新批量操作按钮状态
function updateBatchActions() {
    var selectedCount = $('.item-checkbox:checked').length;
    var totalCount = $('.item-checkbox').length;
    
    $('#select-all').prop('indeterminate', selectedCount > 0 && selectedCount < totalCount);
    $('#select-all').prop('checked', selectedCount === totalCount && totalCount > 0);
    
    if (selectedCount > 0) {
        $('.batch-actions').removeClass('d-none');
        $('.selected-count').text(selectedCount);
    } else {
        $('.batch-actions').addClass('d-none');
    }
}

// 执行批量操作
function performBatchAction(action, items) {
    $.ajax({
        url: '/api/batch-action/',
        method: 'POST',
        data: {
            'action': action,
            'items': items,
            'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(data) {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        },
        error: function() {
            alert('操作失败，请重试');
        }
    });
}

// 初始化图表
function initializeCharts() {
    // 如果页面有图表容器，初始化图表
    if ($('#chart-container').length) {
        // 这里可以集成Chart.js或其他图表库
        console.log('初始化图表');
    }
}

// 格式化数字
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 格式化货币
function formatCurrency(amount) {
    return '¥' + formatNumber(parseFloat(amount).toFixed(2));
}

// 显示加载状态
function showLoading(element) {
    element = $(element);
    element.prop('disabled', true);
    var originalText = element.text();
    element.data('original-text', originalText);
    element.html('<span class="spinner-border spinner-border-sm me-2"></span>加载中...');
}

// 隐藏加载状态
function hideLoading(element) {
    element = $(element);
    element.prop('disabled', false);
    var originalText = element.data('original-text');
    if (originalText) {
        element.text(originalText);
    }
}

// 显示成功消息
function showSuccess(message) {
    showMessage(message, 'success');
}

// 显示错误消息
function showError(message) {
    showMessage(message, 'danger');
}

// 显示消息
function showMessage(message, type) {
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>';
    
    $('.container-fluid').prepend(alertHtml);
    
    // 自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}
