from django.contrib import admin
from .models import AllocationRequest, AllocationRequestItem, Allocation, AllocationItem


class AllocationRequestItemInline(admin.TabularInline):
    model = AllocationRequestItem
    extra = 1
    fields = ('material', 'requested_quantity', 'approved_quantity', 'unit_price', 'notes')


@admin.register(AllocationRequest)
class AllocationRequestAdmin(admin.ModelAdmin):
    list_display = ('request_number', 'title', 'department', 'requester', 'priority', 'status', 'requested_date', 'created_at')
    list_filter = ('status', 'priority', 'department', 'created_at', 'requested_date')
    search_fields = ('request_number', 'title', 'description', 'requester__username')
    ordering = ('-created_at',)
    inlines = [AllocationRequestItemInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('request_number', 'title', 'description', 'department', 'requester')
        }),
        ('申请详情', {
            'fields': ('priority', 'requested_date')
        }),
        ('审批信息', {
            'fields': ('status', 'approver', 'approved_at', 'approval_notes')
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')


class AllocationItemInline(admin.TabularInline):
    model = AllocationItem
    extra = 1
    fields = ('material', 'quantity', 'unit_price', 'warehouse', 'batch_number')


@admin.register(Allocation)
class AllocationAdmin(admin.ModelAdmin):
    list_display = ('allocation_number', 'department', 'allocator', 'total_amount', 'allocated_at')
    list_filter = ('department', 'allocated_at')
    search_fields = ('allocation_number', 'notes', 'allocator__username')
    ordering = ('-allocated_at',)
    inlines = [AllocationItemInline]
    
    def total_amount(self, obj):
        return obj.total_amount
    total_amount.short_description = '总金额'
