<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}物资管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{% load static %}{% static 'css/style.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'accounts:dashboard' %}">
                <i class="fas fa-boxes"></i> 物资管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-box"></i> 物资管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'materials:list' %}">物资列表</a></li>
                            <li><a class="dropdown-item" href="{% url 'materials:create' %}">添加物资</a></li>
                            <li><a class="dropdown-item" href="{% url 'materials:category_list' %}">分类管理</a></li>
                            <li><a class="dropdown-item" href="{% url 'materials:stock_list' %}">库存管理</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-share-alt"></i> 分配管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'allocations:request_list' %}">申请列表</a></li>
                            <li><a class="dropdown-item" href="{% url 'allocations:request_create' %}">创建申请</a></li>
                            <li><a class="dropdown-item" href="{% url 'allocations:allocation_list' %}">分配记录</a></li>
                        </ul>
                    </li>
                    {% if user.is_admin or user.is_manager %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building"></i> 科室管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'departments:list' %}">科室列表</a></li>
                            <li><a class="dropdown-item" href="{% url 'departments:create' %}">添加科室</a></li>
                            <li><a class="dropdown-item" href="{% url 'departments:budget_list' %}">预算管理</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i> 报表分析
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'reports:dashboard' %}">报表仪表板</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:material_stock' %}">库存报表</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:allocation_summary' %}">分配汇总</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:department_usage' %}">科室使用</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:low_stock_alert' %}">库存预警</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">个人资料</a></li>
                            {% if user.is_admin %}
                            <li><a class="dropdown-item" href="{% url 'accounts:user_list' %}">用户管理</a></li>
                            <li><a class="dropdown-item" href="/admin/">系统管理</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">退出登录</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">登录</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid mt-4">
        <!-- 消息提示 -->
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 面包屑导航 -->
        {% block breadcrumb %}{% endblock %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 物资管理系统. All rights reserved.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{% load static %}{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
