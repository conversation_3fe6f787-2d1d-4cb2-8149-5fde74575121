from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
import json

from apps.materials.models import Material, MaterialStock, StockTransaction
from apps.allocations.models import AllocationRequest, Allocation
from apps.departments.models import Department, DepartmentBudget
from .models import ReportTemplate, ReportHistory
from .utils import generate_excel_report, generate_pdf_report


@login_required
def dashboard(request):
    """报表仪表板"""
    # 基础统计数据
    total_materials = Material.objects.filter(is_active=True).count()
    total_departments = Department.objects.filter(is_active=True).count()
    pending_requests = AllocationRequest.objects.filter(status='pending').count()
    low_stock_materials = Material.objects.filter(
        is_active=True
    ).annotate(
        current_stock=Sum('stocks__quantity')
    ).filter(
        current_stock__lte=models.F('min_stock')
    ).count()
    
    # 最近7天的申请趋势
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=6)
    
    daily_requests = []
    for i in range(7):
        date = start_date + timedelta(days=i)
        count = AllocationRequest.objects.filter(
            created_at__date=date
        ).count()
        daily_requests.append({
            'date': date.strftime('%m-%d'),
            'count': count
        })
    
    # 科室分配统计（本月）
    current_month = timezone.now().replace(day=1)
    department_stats = Department.objects.filter(
        is_active=True
    ).annotate(
        allocation_count=Count('allocation_set', filter=Q(
            allocation_set__allocated_at__gte=current_month
        )),
        allocation_amount=Sum('allocation_set__items__total_price', filter=Q(
            allocation_set__allocated_at__gte=current_month
        ))
    ).order_by('-allocation_amount')[:10]
    
    context = {
        'total_materials': total_materials,
        'total_departments': total_departments,
        'pending_requests': pending_requests,
        'low_stock_materials': low_stock_materials,
        'daily_requests': daily_requests,
        'department_stats': department_stats,
    }
    return render(request, 'reports/dashboard.html', context)


class ReportTemplateListView(LoginRequiredMixin, ListView):
    """报表模板列表视图"""
    model = ReportTemplate
    template_name = 'reports/template_list.html'
    context_object_name = 'templates'
    
    def get_queryset(self):
        return ReportTemplate.objects.filter(is_active=True).order_by('-created_at')


@login_required
def material_stock_report(request):
    """物资库存报表"""
    materials = Material.objects.filter(is_active=True).select_related('category')
    
    # 筛选条件
    category_id = request.GET.get('category')
    if category_id:
        materials = materials.filter(category_id=category_id)
    
    low_stock_only = request.GET.get('low_stock')
    if low_stock_only:
        materials = materials.annotate(
            current_stock=Sum('stocks__quantity')
        ).filter(
            current_stock__lte=models.F('min_stock')
        )
    
    # 导出Excel
    if request.GET.get('export') == 'excel':
        return generate_excel_report(materials, 'material_stock_report')
    
    context = {
        'materials': materials,
        'categories': MaterialCategory.objects.filter(is_active=True),
        'selected_category': category_id,
        'low_stock_only': low_stock_only,
    }
    return render(request, 'reports/material_stock_report.html', context)


@login_required
def allocation_summary_report(request):
    """分配汇总报表"""
    # 日期范围
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    allocations = Allocation.objects.select_related('department', 'allocator')
    
    if start_date:
        allocations = allocations.filter(allocated_at__date__gte=start_date)
    if end_date:
        allocations = allocations.filter(allocated_at__date__lte=end_date)
    
    # 按科室统计
    department_stats = allocations.values(
        'department__name'
    ).annotate(
        total_count=Count('id'),
        total_amount=Sum('items__total_price')
    ).order_by('-total_amount')
    
    # 按月份统计
    monthly_stats = allocations.extra(
        select={'month': "DATE_FORMAT(allocated_at, '%%Y-%%m')"}
    ).values('month').annotate(
        total_count=Count('id'),
        total_amount=Sum('items__total_price')
    ).order_by('month')
    
    context = {
        'allocations': allocations.order_by('-allocated_at')[:50],
        'department_stats': department_stats,
        'monthly_stats': monthly_stats,
        'start_date': start_date,
        'end_date': end_date,
    }
    return render(request, 'reports/allocation_summary_report.html', context)


@login_required
def department_usage_report(request):
    """科室使用报表"""
    department_id = request.GET.get('department')
    year = request.GET.get('year', timezone.now().year)
    
    departments = Department.objects.filter(is_active=True)
    if department_id:
        departments = departments.filter(id=department_id)
    
    # 获取科室使用统计
    usage_stats = []
    for dept in departments:
        allocations = dept.allocation_set.filter(
            allocated_at__year=year
        )
        
        total_amount = allocations.aggregate(
            total=Sum('items__total_price')
        )['total'] or 0
        
        total_count = allocations.count()
        
        # 获取预算信息
        try:
            budget = dept.budgets.get(year=year)
            budget_usage = (total_amount / budget.total_budget * 100) if budget.total_budget > 0 else 0
        except DepartmentBudget.DoesNotExist:
            budget = None
            budget_usage = 0
        
        usage_stats.append({
            'department': dept,
            'total_amount': total_amount,
            'total_count': total_count,
            'budget': budget,
            'budget_usage': budget_usage,
        })
    
    context = {
        'usage_stats': usage_stats,
        'departments': Department.objects.filter(is_active=True),
        'selected_department': department_id,
        'selected_year': int(year),
        'years': range(2020, timezone.now().year + 2),
    }
    return render(request, 'reports/department_usage_report.html', context)


@login_required
def low_stock_alert_report(request):
    """库存预警报表"""
    low_stock_materials = Material.objects.filter(
        is_active=True
    ).annotate(
        current_stock=Sum('stocks__quantity')
    ).filter(
        current_stock__lte=models.F('min_stock')
    ).select_related('category')
    
    context = {
        'low_stock_materials': low_stock_materials,
    }
    return render(request, 'reports/low_stock_alert_report.html', context)
