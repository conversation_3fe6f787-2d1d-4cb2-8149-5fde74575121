from django.contrib import admin
from .models import Department, DepartmentBudget


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'head', 'parent', 'is_active', 'created_at')
    list_filter = ('is_active', 'parent', 'created_at')
    search_fields = ('name', 'code', 'description')
    list_editable = ('is_active',)
    ordering = ('code',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'description')
        }),
        ('组织结构', {
            'fields': ('parent', 'head')
        }),
        ('状态', {
            'fields': ('is_active',)
        }),
    )


@admin.register(DepartmentBudget)
class DepartmentBudgetAdmin(admin.ModelAdmin):
    list_display = ('department', 'year', 'total_budget', 'used_budget', 'remaining_budget', 'usage_percentage')
    list_filter = ('year', 'department')
    search_fields = ('department__name', 'department__code')
    ordering = ('-year', 'department__code')
    
    def remaining_budget(self, obj):
        return obj.remaining_budget
    remaining_budget.short_description = '剩余预算'
    
    def usage_percentage(self, obj):
        return f"{obj.usage_percentage:.1f}%"
    usage_percentage.short_description = '使用率'
