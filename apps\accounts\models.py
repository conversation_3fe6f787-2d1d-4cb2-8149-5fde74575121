from django.contrib.auth.models import AbstractUser
from django.db import models


class User(AbstractUser):
    """自定义用户模型"""
    ROLE_CHOICES = [
        ('admin', '系统管理员'),
        ('manager', '物资管理员'),
        ('department_head', '科室主任'),
        ('staff', '普通员工'),
    ]
    
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='staff',
        verbose_name='角色'
    )
    phone = models.CharField(
        max_length=11,
        blank=True,
        null=True,
        verbose_name='手机号'
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'
        db_table = 'accounts_user'
    
    def __str__(self):
        return f"{self.username} ({self.get_role_display()})"
    
    @property
    def is_admin(self):
        return self.role == 'admin'
    
    @property
    def is_manager(self):
        return self.role == 'manager'
    
    @property
    def is_department_head(self):
        return self.role == 'department_head'


class UserProfile(models.Model):
    """用户扩展信息"""
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        verbose_name='用户'
    )
    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        verbose_name='头像'
    )
    bio = models.TextField(
        blank=True,
        null=True,
        verbose_name='个人简介'
    )
    address = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name='地址'
    )
    
    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
        db_table = 'accounts_user_profile'
    
    def __str__(self):
        return f"{self.user.username}的资料"
