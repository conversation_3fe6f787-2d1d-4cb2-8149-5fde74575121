from django import forms
from .models import Department, DepartmentBudget


class DepartmentForm(forms.ModelForm):
    """科室表单"""
    class Meta:
        model = Department
        fields = ('name', 'code', 'description', 'head', 'parent', 'is_active')
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'head': forms.Select(attrs={'class': 'form-control'}),
            'parent': forms.Select(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'name': '科室名称',
            'code': '科室代码',
            'description': '科室描述',
            'head': '科室主任',
            'parent': '上级科室',
            'is_active': '是否启用',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 只显示科室主任角色的用户
        from apps.accounts.models import User
        self.fields['head'].queryset = User.objects.filter(
            role__in=['department_head', 'admin']
        )


class DepartmentBudgetForm(forms.ModelForm):
    """科室预算表单"""
    class Meta:
        model = DepartmentBudget
        fields = ('department', 'year', 'total_budget')
        widgets = {
            'department': forms.Select(attrs={'class': 'form-control'}),
            'year': forms.NumberInput(attrs={'class': 'form-control'}),
            'total_budget': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
        }
        labels = {
            'department': '科室',
            'year': '年度',
            'total_budget': '总预算',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['department'].queryset = Department.objects.filter(is_active=True)
