from django.contrib import admin
from .models import ReportTemplate, ReportHistory


@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'report_type', 'is_active', 'created_by', 'created_at')
    list_filter = ('report_type', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    list_editable = ('is_active',)
    ordering = ('-created_at',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'report_type', 'description')
        }),
        ('配置', {
            'fields': ('parameters', 'is_active')
        }),
    )


@admin.register(ReportHistory)
class ReportHistoryAdmin(admin.ModelAdmin):
    list_display = ('title', 'template', 'file_size', 'generated_by', 'generated_at')
    list_filter = ('template__report_type', 'generated_at')
    search_fields = ('title', 'template__name')
    ordering = ('-generated_at',)
    readonly_fields = ('generated_at',)
