from django import forms
from django.forms import inlineformset_factory
from .models import AllocationRequest, AllocationRequestItem


class AllocationRequestForm(forms.ModelForm):
    """分配申请表单"""
    class Meta:
        model = AllocationRequest
        fields = ('department', 'title', 'description', 'priority', 'requested_date')
        widgets = {
            'department': forms.Select(attrs={'class': 'form-control'}),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'priority': forms.Select(attrs={'class': 'form-control'}),
            'requested_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from apps.departments.models import Department
        self.fields['department'].queryset = Department.objects.filter(is_active=True)


class AllocationRequestItemForm(forms.ModelForm):
    """分配申请明细表单"""
    class Meta:
        model = AllocationRequestItem
        fields = ('material', 'requested_quantity', 'unit_price', 'notes')
        widgets = {
            'material': forms.Select(attrs={'class': 'form-control'}),
            'requested_quantity': forms.NumberInput(attrs={'class': 'form-control'}),
            'unit_price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'notes': forms.TextInput(attrs={'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from apps.materials.models import Material
        self.fields['material'].queryset = Material.objects.filter(is_active=True)
        
        # 如果选择了物资，自动填充单价
        if self.instance.pk and self.instance.material:
            self.fields['unit_price'].initial = self.instance.material.unit_price


# 创建内联表单集
AllocationRequestItemFormSet = inlineformset_factory(
    AllocationRequest,
    AllocationRequestItem,
    form=AllocationRequestItemForm,
    extra=1,
    can_delete=True,
    min_num=1,
    validate_min=True
)


class AllocationApprovalForm(forms.ModelForm):
    """分配申请审批表单"""
    class Meta:
        model = AllocationRequest
        fields = ('status', 'approval_notes')
        widgets = {
            'status': forms.Select(attrs={'class': 'form-control'}),
            'approval_notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 只显示审批相关的状态选项
        self.fields['status'].choices = [
            ('approved', '批准'),
            ('rejected', '拒绝'),
        ]
