from django.db import models
from django.core.validators import MinValueValidator


class AllocationRequest(models.Model):
    """物资分配申请模型"""
    STATUS_CHOICES = [
        ('pending', '待审批'),
        ('approved', '已批准'),
        ('rejected', '已拒绝'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('urgent', '紧急'),
    ]
    
    request_number = models.CharField(max_length=50, unique=True, verbose_name='申请单号')
    department = models.ForeignKey(
        'departments.Department',
        on_delete=models.CASCADE,
        related_name='allocation_requests',
        verbose_name='申请科室'
    )
    requester = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='allocation_requests',
        verbose_name='申请人'
    )
    title = models.CharField(max_length=200, verbose_name='申请标题')
    description = models.TextField(verbose_name='申请说明')
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='normal',
        verbose_name='优先级'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )
    requested_date = models.DateField(verbose_name='需求日期')
    approver = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_requests',
        verbose_name='审批人'
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name='审批时间')
    approval_notes = models.TextField(blank=True, null=True, verbose_name='审批备注')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '物资分配申请'
        verbose_name_plural = '物资分配申请'
        db_table = 'allocations_request'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.request_number} - {self.title}"
    
    @property
    def total_amount(self):
        """申请总金额"""
        return sum(item.total_price for item in self.items.all())
    
    def can_approve(self, user):
        """检查用户是否可以审批"""
        return user.is_manager or user.is_admin
    
    def can_edit(self, user):
        """检查用户是否可以编辑"""
        return (self.requester == user and self.status == 'pending') or user.is_manager or user.is_admin


class AllocationRequestItem(models.Model):
    """物资分配申请明细模型"""
    request = models.ForeignKey(
        AllocationRequest,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='申请单'
    )
    material = models.ForeignKey(
        'materials.Material',
        on_delete=models.CASCADE,
        verbose_name='物资'
    )
    requested_quantity = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        verbose_name='申请数量'
    )
    approved_quantity = models.PositiveIntegerField(
        default=0,
        verbose_name='批准数量'
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='单价'
    )
    notes = models.TextField(blank=True, null=True, verbose_name='备注')
    
    class Meta:
        verbose_name = '申请明细'
        verbose_name_plural = '申请明细'
        db_table = 'allocations_request_item'
        unique_together = ['request', 'material']
    
    def __str__(self):
        return f"{self.request.request_number} - {self.material.name}"
    
    @property
    def total_price(self):
        """总价"""
        return self.requested_quantity * self.unit_price
    
    @property
    def approved_total_price(self):
        """批准总价"""
        return self.approved_quantity * self.unit_price


class Allocation(models.Model):
    """物资分配记录模型"""
    allocation_number = models.CharField(max_length=50, unique=True, verbose_name='分配单号')
    request = models.ForeignKey(
        AllocationRequest,
        on_delete=models.CASCADE,
        related_name='allocations',
        verbose_name='申请单'
    )
    department = models.ForeignKey(
        'departments.Department',
        on_delete=models.CASCADE,
        verbose_name='分配科室'
    )
    allocator = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='allocations',
        verbose_name='分配人'
    )
    allocated_at = models.DateTimeField(auto_now_add=True, verbose_name='分配时间')
    notes = models.TextField(blank=True, null=True, verbose_name='分配备注')
    
    class Meta:
        verbose_name = '物资分配记录'
        verbose_name_plural = '物资分配记录'
        db_table = 'allocations_allocation'
        ordering = ['-allocated_at']
    
    def __str__(self):
        return f"{self.allocation_number} - {self.department.name}"
    
    @property
    def total_amount(self):
        """分配总金额"""
        return sum(item.total_price for item in self.items.all())


class AllocationItem(models.Model):
    """物资分配明细模型"""
    allocation = models.ForeignKey(
        Allocation,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='分配单'
    )
    material = models.ForeignKey(
        'materials.Material',
        on_delete=models.CASCADE,
        verbose_name='物资'
    )
    quantity = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        verbose_name='分配数量'
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='单价'
    )
    warehouse = models.CharField(max_length=100, verbose_name='出库仓库')
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='批次号')
    
    class Meta:
        verbose_name = '分配明细'
        verbose_name_plural = '分配明细'
        db_table = 'allocations_allocation_item'
    
    def __str__(self):
        return f"{self.allocation.allocation_number} - {self.material.name}"
    
    @property
    def total_price(self):
        """总价"""
        return self.quantity * self.unit_price
