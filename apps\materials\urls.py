from django.urls import path
from . import views

app_name = 'materials'

urlpatterns = [
    # 物资管理
    path('', views.MaterialListView.as_view(), name='list'),
    path('create/', views.MaterialCreateView.as_view(), name='create'),
    path('<int:pk>/', views.material_detail, name='detail'),
    path('<int:pk>/edit/', views.MaterialUpdateView.as_view(), name='edit'),
    
    # 分类管理
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('categories/create/', views.CategoryCreateView.as_view(), name='category_create'),
    
    # 库存管理
    path('stocks/', views.stock_list, name='stock_list'),
    path('stocks/transaction/', views.stock_transaction_create, name='stock_transaction'),
]
