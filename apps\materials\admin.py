from django.contrib import admin
from .models import MaterialCategory, Material, MaterialStock, StockTransaction


@admin.register(MaterialCategory)
class MaterialCategoryAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'parent', 'is_active', 'created_at')
    list_filter = ('is_active', 'parent', 'created_at')
    search_fields = ('name', 'code', 'description')
    list_editable = ('is_active',)
    ordering = ('code',)


@admin.register(Material)
class MaterialAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'category', 'unit_price', 'unit', 'current_stock', 'is_low_stock', 'is_active')
    list_filter = ('category', 'unit', 'is_active', 'created_at')
    search_fields = ('name', 'code', 'specification', 'supplier')
    list_editable = ('is_active',)
    ordering = ('code',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'code', 'category', 'specification', 'unit')
        }),
        ('价格信息', {
            'fields': ('unit_price', 'supplier')
        }),
        ('库存设置', {
            'fields': ('min_stock', 'max_stock')
        }),
        ('其他信息', {
            'fields': ('description', 'image', 'is_active')
        }),
    )
    
    def current_stock(self, obj):
        return obj.current_stock
    current_stock.short_description = '当前库存'
    
    def is_low_stock(self, obj):
        return obj.is_low_stock
    is_low_stock.short_description = '库存不足'
    is_low_stock.boolean = True


@admin.register(MaterialStock)
class MaterialStockAdmin(admin.ModelAdmin):
    list_display = ('material', 'warehouse', 'quantity', 'batch_number', 'expiry_date', 'created_at')
    list_filter = ('warehouse', 'created_at', 'expiry_date')
    search_fields = ('material__name', 'material__code', 'batch_number')
    ordering = ('-created_at',)


@admin.register(StockTransaction)
class StockTransactionAdmin(admin.ModelAdmin):
    list_display = ('material', 'transaction_type', 'quantity', 'warehouse', 'operator', 'created_at')
    list_filter = ('transaction_type', 'warehouse', 'created_at')
    search_fields = ('material__name', 'material__code', 'reference_number', 'notes')
    ordering = ('-created_at',)
    readonly_fields = ('created_at',)
