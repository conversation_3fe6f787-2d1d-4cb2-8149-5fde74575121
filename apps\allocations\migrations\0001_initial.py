# Generated by Django 3.2.25 on 2025-06-30 03:29

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('materials', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('departments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Allocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('allocation_number', models.CharField(max_length=50, unique=True, verbose_name='分配单号')),
                ('allocated_at', models.DateTimeField(auto_now_add=True, verbose_name='分配时间')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='分配备注')),
                ('allocator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to=settings.AUTH_USER_MODEL, verbose_name='分配人')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='departments.department', verbose_name='分配科室')),
            ],
            options={
                'verbose_name': '物资分配记录',
                'verbose_name_plural': '物资分配记录',
                'db_table': 'allocations_allocation',
                'ordering': ['-allocated_at'],
            },
        ),
        migrations.CreateModel(
            name='AllocationRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_number', models.CharField(max_length=50, unique=True, verbose_name='申请单号')),
                ('title', models.CharField(max_length=200, verbose_name='申请标题')),
                ('description', models.TextField(verbose_name='申请说明')),
                ('priority', models.CharField(choices=[('low', '低'), ('normal', '普通'), ('high', '高'), ('urgent', '紧急')], default='normal', max_length=20, verbose_name='优先级')),
                ('status', models.CharField(choices=[('pending', '待审批'), ('approved', '已批准'), ('rejected', '已拒绝'), ('completed', '已完成'), ('cancelled', '已取消')], default='pending', max_length=20, verbose_name='状态')),
                ('requested_date', models.DateField(verbose_name='需求日期')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='审批时间')),
                ('approval_notes', models.TextField(blank=True, null=True, verbose_name='审批备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_requests', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allocation_requests', to='departments.department', verbose_name='申请科室')),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allocation_requests', to=settings.AUTH_USER_MODEL, verbose_name='申请人')),
            ],
            options={
                'verbose_name': '物资分配申请',
                'verbose_name_plural': '物资分配申请',
                'db_table': 'allocations_request',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AllocationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='分配数量')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='单价')),
                ('warehouse', models.CharField(max_length=100, verbose_name='出库仓库')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='批次号')),
                ('allocation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='allocations.allocation', verbose_name='分配单')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='materials.material', verbose_name='物资')),
            ],
            options={
                'verbose_name': '分配明细',
                'verbose_name_plural': '分配明细',
                'db_table': 'allocations_allocation_item',
            },
        ),
        migrations.AddField(
            model_name='allocation',
            name='request',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allocations', to='allocations.allocationrequest', verbose_name='申请单'),
        ),
        migrations.CreateModel(
            name='AllocationRequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requested_quantity', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='申请数量')),
                ('approved_quantity', models.PositiveIntegerField(default=0, verbose_name='批准数量')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='单价')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='materials.material', verbose_name='物资')),
                ('request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='allocations.allocationrequest', verbose_name='申请单')),
            ],
            options={
                'verbose_name': '申请明细',
                'verbose_name_plural': '申请明细',
                'db_table': 'allocations_request_item',
                'unique_together': {('request', 'material')},
            },
        ),
    ]
