from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.db.models import Q, Sum
from .models import Material, MaterialCategory, MaterialStock, StockTransaction
from .forms import MaterialForm, MaterialCategoryForm, StockTransactionForm


class MaterialListView(LoginRequiredMixin, ListView):
    """物资列表视图"""
    model = Material
    template_name = 'materials/material_list.html'
    context_object_name = 'materials'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Material.objects.filter(is_active=True).select_related('category')
        
        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(specification__icontains=search)
            )
        
        # 分类筛选
        category_id = self.request.GET.get('category')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        return queryset.order_by('code')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = MaterialCategory.objects.filter(is_active=True)
        context['search'] = self.request.GET.get('search', '')
        context['selected_category'] = self.request.GET.get('category', '')
        return context


class MaterialCreateView(LoginRequiredMixin, CreateView):
    """创建物资视图"""
    model = Material
    form_class = MaterialForm
    template_name = 'materials/material_form.html'
    success_url = reverse_lazy('materials:list')
    
    def form_valid(self, form):
        messages.success(self.request, '物资创建成功。')
        return super().form_valid(form)


class MaterialUpdateView(LoginRequiredMixin, UpdateView):
    """更新物资视图"""
    model = Material
    form_class = MaterialForm
    template_name = 'materials/material_form.html'
    success_url = reverse_lazy('materials:list')
    
    def form_valid(self, form):
        messages.success(self.request, '物资信息更新成功。')
        return super().form_valid(form)


@login_required
def material_detail(request, pk):
    """物资详情"""
    material = get_object_or_404(Material, pk=pk)
    stocks = material.stocks.all()
    recent_transactions = material.transactions.all()[:10]
    
    context = {
        'material': material,
        'stocks': stocks,
        'recent_transactions': recent_transactions,
    }
    return render(request, 'materials/material_detail.html', context)


class CategoryListView(LoginRequiredMixin, ListView):
    """分类列表视图"""
    model = MaterialCategory
    template_name = 'materials/category_list.html'
    context_object_name = 'categories'
    
    def get_queryset(self):
        return MaterialCategory.objects.filter(is_active=True).order_by('code')


class CategoryCreateView(LoginRequiredMixin, CreateView):
    """创建分类视图"""
    model = MaterialCategory
    form_class = MaterialCategoryForm
    template_name = 'materials/category_form.html'
    success_url = reverse_lazy('materials:category_list')
    
    def form_valid(self, form):
        messages.success(self.request, '分类创建成功。')
        return super().form_valid(form)


@login_required
def stock_list(request):
    """库存列表"""
    stocks = MaterialStock.objects.select_related('material').order_by('-created_at')
    
    # 搜索功能
    search = request.GET.get('search')
    if search:
        stocks = stocks.filter(
            Q(material__name__icontains=search) |
            Q(material__code__icontains=search) |
            Q(warehouse__icontains=search)
        )
    
    context = {
        'stocks': stocks,
        'search': search,
    }
    return render(request, 'materials/stock_list.html', context)


@login_required
def stock_transaction_create(request):
    """创建库存变动"""
    if request.method == 'POST':
        form = StockTransactionForm(request.POST)
        if form.is_valid():
            transaction = form.save(commit=False)
            transaction.operator = request.user
            transaction.save()
            
            # 更新库存
            material = transaction.material
            stock, created = MaterialStock.objects.get_or_create(
                material=material,
                warehouse=transaction.warehouse,
                defaults={'quantity': 0}
            )
            
            if transaction.transaction_type == 'in':
                stock.quantity += transaction.quantity
            elif transaction.transaction_type == 'out':
                stock.quantity -= transaction.quantity
            elif transaction.transaction_type == 'adjust':
                stock.quantity = transaction.quantity
            
            stock.save()
            
            messages.success(request, '库存变动记录创建成功。')
            return redirect('materials:stock_list')
    else:
        form = StockTransactionForm()
    
    return render(request, 'materials/stock_transaction_form.html', {'form': form})
