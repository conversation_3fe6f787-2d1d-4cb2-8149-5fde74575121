from django.db import models


class Department(models.Model):
    """科室模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name='科室名称')
    code = models.CharField(max_length=20, unique=True, verbose_name='科室代码')
    description = models.TextField(blank=True, null=True, verbose_name='科室描述')
    head = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments',
        verbose_name='科室主任'
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='上级科室'
    )
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '科室'
        verbose_name_plural = '科室'
        db_table = 'departments_department'
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def full_name(self):
        """获取完整科室名称（包含上级科室）"""
        if self.parent:
            return f"{self.parent.full_name} > {self.name}"
        return self.name
    
    def get_all_children(self):
        """获取所有子科室"""
        children = []
        for child in self.children.all():
            children.append(child)
            children.extend(child.get_all_children())
        return children


class DepartmentBudget(models.Model):
    """科室预算模型"""
    department = models.ForeignKey(
        Department,
        on_delete=models.CASCADE,
        related_name='budgets',
        verbose_name='科室'
    )
    year = models.IntegerField(verbose_name='年度')
    total_budget = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name='总预算'
    )
    used_budget = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='已使用预算'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '科室预算'
        verbose_name_plural = '科室预算'
        db_table = 'departments_budget'
        unique_together = ['department', 'year']
        ordering = ['-year']
    
    def __str__(self):
        return f"{self.department.name} - {self.year}年预算"
    
    @property
    def remaining_budget(self):
        """剩余预算"""
        return self.total_budget - self.used_budget
    
    @property
    def usage_percentage(self):
        """预算使用率"""
        if self.total_budget > 0:
            return (self.used_budget / self.total_budget) * 100
        return 0
