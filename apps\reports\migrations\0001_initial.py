# Generated by Django 3.2.25 on 2025-06-30 03:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模板名称')),
                ('report_type', models.CharField(choices=[('material_stock', '物资库存报表'), ('allocation_summary', '分配汇总报表'), ('department_usage', '科室使用报表'), ('budget_analysis', '预算分析报表'), ('low_stock_alert', '库存预警报表')], max_length=50, verbose_name='报表类型')),
                ('description', models.TextField(blank=True, null=True, verbose_name='模板描述')),
                ('parameters', models.TextField(default='{}', verbose_name='报表参数')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '报表模板',
                'verbose_name_plural': '报表模板',
                'db_table': 'reports_template',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='报表标题')),
                ('parameters', models.TextField(default='{}', verbose_name='生成参数')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='文件路径')),
                ('file_size', models.PositiveIntegerField(default=0, verbose_name='文件大小')),
                ('generated_at', models.DateTimeField(auto_now_add=True, verbose_name='生成时间')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='生成人')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='histories', to='reports.reporttemplate', verbose_name='报表模板')),
            ],
            options={
                'verbose_name': '报表历史',
                'verbose_name_plural': '报表历史',
                'db_table': 'reports_history',
                'ordering': ['-generated_at'],
            },
        ),
    ]
