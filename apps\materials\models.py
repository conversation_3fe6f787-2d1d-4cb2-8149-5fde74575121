from django.db import models
from django.core.validators import MinValueValidator


class MaterialCategory(models.Model):
    """物资分类模型"""
    name = models.CharField(max_length=100, unique=True, verbose_name='分类名称')
    code = models.CharField(max_length=20, unique=True, verbose_name='分类代码')
    description = models.TextField(blank=True, null=True, verbose_name='分类描述')
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='上级分类'
    )
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '物资分类'
        verbose_name_plural = '物资分类'
        db_table = 'materials_category'
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.name}"


class Material(models.Model):
    """物资模型"""
    UNIT_CHOICES = [
        ('piece', '件'),
        ('box', '盒'),
        ('bottle', '瓶'),
        ('bag', '袋'),
        ('kg', '公斤'),
        ('meter', '米'),
        ('set', '套'),
        ('pair', '对'),
    ]
    
    name = models.CharField(max_length=200, verbose_name='物资名称')
    code = models.CharField(max_length=50, unique=True, verbose_name='物资编码')
    category = models.ForeignKey(
        MaterialCategory,
        on_delete=models.CASCADE,
        related_name='materials',
        verbose_name='物资分类'
    )
    specification = models.CharField(max_length=200, blank=True, null=True, verbose_name='规格型号')
    unit = models.CharField(max_length=20, choices=UNIT_CHOICES, verbose_name='计量单位')
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='单价'
    )
    supplier = models.CharField(max_length=200, blank=True, null=True, verbose_name='供应商')
    description = models.TextField(blank=True, null=True, verbose_name='物资描述')
    image = models.ImageField(
        upload_to='materials/',
        blank=True,
        null=True,
        verbose_name='物资图片'
    )
    min_stock = models.PositiveIntegerField(default=0, verbose_name='最低库存')
    max_stock = models.PositiveIntegerField(default=0, verbose_name='最高库存')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '物资'
        verbose_name_plural = '物资'
        db_table = 'materials_material'
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def current_stock(self):
        """当前库存"""
        return self.stocks.aggregate(
            total=models.Sum('quantity')
        )['total'] or 0
    
    @property
    def is_low_stock(self):
        """是否库存不足"""
        return self.current_stock <= self.min_stock
    
    @property
    def is_over_stock(self):
        """是否库存过多"""
        return self.current_stock >= self.max_stock


class MaterialStock(models.Model):
    """物资库存模型"""
    material = models.ForeignKey(
        Material,
        on_delete=models.CASCADE,
        related_name='stocks',
        verbose_name='物资'
    )
    warehouse = models.CharField(max_length=100, verbose_name='仓库位置')
    quantity = models.PositiveIntegerField(verbose_name='库存数量')
    batch_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='批次号')
    production_date = models.DateField(blank=True, null=True, verbose_name='生产日期')
    expiry_date = models.DateField(blank=True, null=True, verbose_name='过期日期')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '物资库存'
        verbose_name_plural = '物资库存'
        db_table = 'materials_stock'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.material.name} - {self.warehouse} - {self.quantity}{self.material.unit}"


class StockTransaction(models.Model):
    """库存变动记录模型"""
    TRANSACTION_TYPES = [
        ('in', '入库'),
        ('out', '出库'),
        ('transfer', '调拨'),
        ('adjust', '调整'),
    ]
    
    material = models.ForeignKey(
        Material,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name='物资'
    )
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        verbose_name='变动类型'
    )
    quantity = models.IntegerField(verbose_name='变动数量')
    warehouse = models.CharField(max_length=100, verbose_name='仓库位置')
    reference_number = models.CharField(max_length=50, blank=True, null=True, verbose_name='参考单号')
    operator = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        verbose_name='操作员'
    )
    notes = models.TextField(blank=True, null=True, verbose_name='备注')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='操作时间')
    
    class Meta:
        verbose_name = '库存变动记录'
        verbose_name_plural = '库存变动记录'
        db_table = 'materials_stock_transaction'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.material.name} - {self.get_transaction_type_display()} - {self.quantity}"
