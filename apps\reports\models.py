from django.db import models


class ReportTemplate(models.Model):
    """报表模板模型"""
    REPORT_TYPES = [
        ('material_stock', '物资库存报表'),
        ('allocation_summary', '分配汇总报表'),
        ('department_usage', '科室使用报表'),
        ('budget_analysis', '预算分析报表'),
        ('low_stock_alert', '库存预警报表'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='模板名称')
    report_type = models.CharField(max_length=50, choices=REPORT_TYPES, verbose_name='报表类型')
    description = models.TextField(blank=True, null=True, verbose_name='模板描述')
    parameters = models.JSONField(default=dict, verbose_name='报表参数')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        verbose_name='创建人'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '报表模板'
        verbose_name_plural = '报表模板'
        db_table = 'reports_template'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_report_type_display()})"


class ReportHistory(models.Model):
    """报表生成历史模型"""
    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='histories',
        verbose_name='报表模板'
    )
    title = models.CharField(max_length=200, verbose_name='报表标题')
    parameters = models.JSONField(default=dict, verbose_name='生成参数')
    file_path = models.CharField(max_length=500, blank=True, null=True, verbose_name='文件路径')
    file_size = models.PositiveIntegerField(default=0, verbose_name='文件大小')
    generated_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        verbose_name='生成人'
    )
    generated_at = models.DateTimeField(auto_now_add=True, verbose_name='生成时间')
    
    class Meta:
        verbose_name = '报表历史'
        verbose_name_plural = '报表历史'
        db_table = 'reports_history'
        ordering = ['-generated_at']
    
    def __str__(self):
        return f"{self.title} - {self.generated_at.strftime('%Y-%m-%d %H:%M')}"
