{% extends 'base.html' %}
{% load static %}

{% block title %}仪表板 - 物资管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-tachometer-alt"></i> 仪表板</h2>
        <p class="text-muted">欢迎回来，{{ user.get_full_name|default:user.username }}！</p>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>物资总数</h4>
                        <h2>{{ total_materials|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>科室数量</h4>
                        <h2>{{ total_departments|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>待审批</h4>
                        <h2>{{ pending_requests|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>库存预警</h4>
                        <h2>{{ low_stock_materials|default:0 }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快捷操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> 快捷操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'allocations:request_create' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus"></i> 创建申请
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'materials:create' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-box"></i> 添加物资
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'materials:stock_transaction' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-exchange-alt"></i> 库存变动
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'reports:dashboard' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-chart-bar"></i> 查看报表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> 最近申请</h5>
            </div>
            <div class="card-body">
                {% if recent_allocations %}
                    <div class="list-group list-group-flush">
                        {% for allocation in recent_allocations %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ allocation.title }}</h6>
                                <small>{{ allocation.created_at|date:"m-d H:i" }}</small>
                            </div>
                            <p class="mb-1">{{ allocation.department.name }}</p>
                            <small class="text-muted">状态: {{ allocation.get_status_display }}</small>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">暂无最近申请记录</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tasks"></i> 待处理事项</h5>
            </div>
            <div class="card-body">
                {% if pending_requests > 0 %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        您有 {{ pending_requests }} 个待审批的申请
                        <a href="{% url 'allocations:request_list' %}?status=pending" class="alert-link">立即处理</a>
                    </div>
                {% endif %}
                
                {% if low_stock_materials > 0 %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        有 {{ low_stock_materials }} 种物资库存不足
                        <a href="{% url 'reports:low_stock_alert' %}" class="alert-link">查看详情</a>
                    </div>
                {% endif %}
                
                {% if pending_requests == 0 and low_stock_materials == 0 %}
                    <p class="text-success">
                        <i class="fas fa-check-circle"></i> 暂无待处理事项
                    </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
