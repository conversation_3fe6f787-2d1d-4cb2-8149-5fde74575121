from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from .models import Department, DepartmentBudget
from .forms import DepartmentForm, DepartmentBudgetForm


class DepartmentListView(LoginRequiredMixin, ListView):
    """科室列表视图"""
    model = Department
    template_name = 'departments/department_list.html'
    context_object_name = 'departments'
    paginate_by = 20
    
    def get_queryset(self):
        return Department.objects.filter(is_active=True).select_related('head', 'parent')


class DepartmentCreateView(LoginRequiredMixin, CreateView):
    """创建科室视图"""
    model = Department
    form_class = DepartmentForm
    template_name = 'departments/department_form.html'
    success_url = reverse_lazy('departments:list')
    
    def form_valid(self, form):
        messages.success(self.request, '科室创建成功。')
        return super().form_valid(form)


class DepartmentUpdateView(LoginRequiredMixin, UpdateView):
    """更新科室视图"""
    model = Department
    form_class = DepartmentForm
    template_name = 'departments/department_form.html'
    success_url = reverse_lazy('departments:list')
    
    def form_valid(self, form):
        messages.success(self.request, '科室信息更新成功。')
        return super().form_valid(form)


class DepartmentDeleteView(LoginRequiredMixin, DeleteView):
    """删除科室视图"""
    model = Department
    template_name = 'departments/department_confirm_delete.html'
    success_url = reverse_lazy('departments:list')
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, '科室删除成功。')
        return super().delete(request, *args, **kwargs)


@login_required
def department_detail(request, pk):
    """科室详情"""
    department = get_object_or_404(Department, pk=pk)
    budgets = department.budgets.all()
    staff_count = department.user_set.count()
    
    context = {
        'department': department,
        'budgets': budgets,
        'staff_count': staff_count,
    }
    return render(request, 'departments/department_detail.html', context)


class BudgetListView(LoginRequiredMixin, ListView):
    """预算列表视图"""
    model = DepartmentBudget
    template_name = 'departments/budget_list.html'
    context_object_name = 'budgets'
    paginate_by = 20
    
    def get_queryset(self):
        return DepartmentBudget.objects.select_related('department').order_by('-year', 'department__code')


class BudgetCreateView(LoginRequiredMixin, CreateView):
    """创建预算视图"""
    model = DepartmentBudget
    form_class = DepartmentBudgetForm
    template_name = 'departments/budget_form.html'
    success_url = reverse_lazy('departments:budget_list')
    
    def form_valid(self, form):
        messages.success(self.request, '预算创建成功。')
        return super().form_valid(form)
