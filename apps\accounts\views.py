from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView, LogoutView
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from .models import User, UserProfile
from .forms import UserRegistrationForm, UserProfileForm


class CustomLoginView(LoginView):
    """自定义登录视图"""
    template_name = 'accounts/login.html'
    redirect_authenticated_user = True
    
    def get_success_url(self):
        return reverse_lazy('accounts:dashboard')


class CustomLogoutView(LogoutView):
    """自定义登出视图"""
    next_page = reverse_lazy('accounts:login')


@login_required
def dashboard(request):
    """仪表板"""
    context = {
        'user': request.user,
        'recent_allocations': [],  # 后续添加最近的分配记录
        'pending_requests': [],   # 后续添加待处理的申请
    }
    return render(request, 'accounts/dashboard.html', context)


class UserRegistrationView(CreateView):
    """用户注册视图"""
    model = User
    form_class = UserRegistrationForm
    template_name = 'accounts/register.html'
    success_url = reverse_lazy('accounts:login')
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, '注册成功，请登录。')
        return response


class ProfileUpdateView(LoginRequiredMixin, UpdateView):
    """用户资料更新视图"""
    model = UserProfile
    form_class = UserProfileForm
    template_name = 'accounts/profile.html'
    success_url = reverse_lazy('accounts:profile')
    
    def get_object(self, queryset=None):
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, '资料更新成功。')
        return response


@login_required
def user_list(request):
    """用户列表"""
    if not request.user.is_admin:
        messages.error(request, '您没有权限访问此页面。')
        return redirect('accounts:dashboard')
    
    users = User.objects.all().order_by('-date_joined')
    context = {'users': users}
    return render(request, 'accounts/user_list.html', context)
