# Generated by Django 3.2.25 on 2025-06-30 03:29

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Material',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='物资名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='物资编码')),
                ('specification', models.CharField(blank=True, max_length=200, null=True, verbose_name='规格型号')),
                ('unit', models.CharField(choices=[('piece', '件'), ('box', '盒'), ('bottle', '瓶'), ('bag', '袋'), ('kg', '公斤'), ('meter', '米'), ('set', '套'), ('pair', '对')], max_length=20, verbose_name='计量单位')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='单价')),
                ('supplier', models.CharField(blank=True, max_length=200, null=True, verbose_name='供应商')),
                ('description', models.TextField(blank=True, null=True, verbose_name='物资描述')),
                ('image', models.ImageField(blank=True, null=True, upload_to='materials/', verbose_name='物资图片')),
                ('min_stock', models.PositiveIntegerField(default=0, verbose_name='最低库存')),
                ('max_stock', models.PositiveIntegerField(default=0, verbose_name='最高库存')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '物资',
                'verbose_name_plural': '物资',
                'db_table': 'materials_material',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='StockTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('in', '入库'), ('out', '出库'), ('transfer', '调拨'), ('adjust', '调整')], max_length=20, verbose_name='变动类型')),
                ('quantity', models.IntegerField(verbose_name='变动数量')),
                ('warehouse', models.CharField(max_length=100, verbose_name='仓库位置')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='参考单号')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='操作时间')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='materials.material', verbose_name='物资')),
                ('operator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='操作员')),
            ],
            options={
                'verbose_name': '库存变动记录',
                'verbose_name_plural': '库存变动记录',
                'db_table': 'materials_stock_transaction',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MaterialStock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('warehouse', models.CharField(max_length=100, verbose_name='仓库位置')),
                ('quantity', models.PositiveIntegerField(verbose_name='库存数量')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='批次号')),
                ('production_date', models.DateField(blank=True, null=True, verbose_name='生产日期')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='过期日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('material', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stocks', to='materials.material', verbose_name='物资')),
            ],
            options={
                'verbose_name': '物资库存',
                'verbose_name_plural': '物资库存',
                'db_table': 'materials_stock',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MaterialCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='分类名称')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='分类代码')),
                ('description', models.TextField(blank=True, null=True, verbose_name='分类描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='materials.materialcategory', verbose_name='上级分类')),
            ],
            options={
                'verbose_name': '物资分类',
                'verbose_name_plural': '物资分类',
                'db_table': 'materials_category',
                'ordering': ['code'],
            },
        ),
        migrations.AddField(
            model_name='material',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='materials', to='materials.materialcategory', verbose_name='物资分类'),
        ),
    ]
